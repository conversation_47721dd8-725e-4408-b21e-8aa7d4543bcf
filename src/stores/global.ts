import { ref } from 'vue';
import { defineStore } from 'pinia';
import type { MenuGroup, MenuLink, MenuType, System, SystemGroup } from 'src/types/app';

export const useGlobalStore = defineStore('global', () => {
  const menuGroups = ref<MenuGroup[]>([
    {
      type: 'quiz',
      items: [],
    },
    {
      type: 'form',
      items: [
        { title: 'แบบสอบถาม', icon: 'list_alt', link: '/evaluate', perId: [1, 2, 3] },
        { title: 'จัดการแบบสอบถาม', icon: 'list_alt', link: '/evaluate/management', perId: [1, 2] },
      ],
    },
  ]);

  const systemGroups = ref<SystemGroup[]>([
    {
      type: 'ums',
      systems: [
        {
          sysId: 1,
          sysUrl: 'ums',
          sysIconSrc: 'group',
          sysNameEn: 'ums',
          sysNameTh: 'จัดการสิทธิ์ในระบบ',
          perId: [1, 2],
        },
      ],
    },
    {
      type: 'quiz',
      systems: [
        {
          sysId: 2,
          sysUrl: 'quiz',
          sysIconSrc: 'quiz',
          sysNameEn: 'quiz',
          sysNameTh: 'ระบบแบบทดสอบ',
          perId: [1, 2],
        },
      ],
    },
    {
      type: 'form',
      systems: [
        {
          sysId: 3,
          sysUrl: '/evaluate',
          sysIconSrc: 'assignment',
          sysNameEn: 'form',
          sysNameTh: 'ระบบแบบสอบถาม',
          perId: [1, 2, 3],
        },
      ],
    },
  ]);

  const leftDrawerState = ref<boolean>(false);

  const toggleLeftDrawer = (state?: boolean) => {
    if (typeof state === 'undefined') {
      leftDrawerState.value = !leftDrawerState.value;
    } else {
      leftDrawerState.value = state;
    }
  };

  const breadcrumbMap = ref<Record<MenuType, MenuLink[]>>({
    ums: [
      { title: 'หน้าหลัก', icon: 'home', link: '/' },
      { title: 'จัดการสิทธิ์ในระบบ', icon: 'group', link: '/ums' },
    ],
    quiz: [
      { title: 'หน้าหลัก', icon: 'home', link: '/' },
      { title: 'ระบบจัดการแบบทดสอบ', icon: 'quiz', link: '/quiz/management' },
      { title: 'สร้างแบบทดสอบ', icon: 'post_add', link: '/quiz/management/create-quiz' },
      { title: 'การตอบกลับแบบทดสอบ', icon: 'quiz', link: '/quiz/management/responses' },
      { title: 'ตั้งค่าแบบทดสอบ', icon: 'settings', link: '/quiz/management/settings' },
      { title: 'ทำแบบทดสอบ', icon: 'assignment', link: '/quiz/user/management' },
    ],
    form: [
      { title: 'หน้าหลัก', icon: 'home', link: '/home' },
      { title: 'ระบบจัดการแบบสอบถาม', icon: 'assignment', link: '/evaluate/management' },
      {
        title: 'ดูตัวอย่างแบบสอบถาม',
        icon: 'post_add',
        link: '/evaluate/management/preview-evaluate',
      },
      { title: 'สร้างแบบสอบถาม', icon: 'post_add', link: '/evaluate/management/create-evaluate' },
      { title: 'การตอบกลับแบบสอบถาม', icon: 'reply', link: '/evaluate/management/responses' },
      { title: 'ตั้งค่าแบบสอบถาม', icon: 'settings', link: '/evaluate/management/settings' },
      { title: 'ทำแบบสอบถาม', icon: 'post_add', link: '/evaluate/user/do-evaluate' },
    ],
    default: [],
  });

  const menuItems = ref<MenuLink[]>([]);
  const systemCards = ref<System[]>([]);
  const breadcrumbItems = ref<MenuLink[]>([]);
  const activeMenuType = ref<MenuType>('default');
  const currentQuizTitle = ref<string>('');
  const quizTitles = ref<Map<string, string>>(new Map());
  const saveLoading = ref(false);

  // Enhanced save status management
  const saveStatus = ref<'idle' | 'saving' | 'success' | 'error'>('idle');
  const saveMessage = ref<string>('');
  const saveOperationsCount = ref<number>(0);

  const loadQuizTitlesFromStorage = () => {
    try {
      const storedTitles = localStorage.getItem('quizTitles');
      if (storedTitles) {
        const parsedTitles = JSON.parse(storedTitles);
        quizTitles.value = new Map(Object.entries(parsedTitles));
      }
    } catch (error) {
      console.error('Error loading quiz titles from localStorage:', error);
    }
  };

  const saveQuizTitlesToStorage = () => {
    try {
      const titlesObject = Object.fromEntries(quizTitles.value);
      localStorage.setItem('quizTitles', JSON.stringify(titlesObject));
    } catch (error) {
      console.error('Error saving quiz titles to localStorage:', error);
    }
  };
  loadQuizTitlesFromStorage();

  function setMenuItems(items: MenuLink[]) {
    menuItems.value = items;
  }

  function setMenuGroup(type: MenuType, items: MenuLink[]) {
    const groupIndex = menuGroups.value.findIndex((group) => group.type === type);
    if (groupIndex >= 0 && menuGroups.value[groupIndex]) {
      menuGroups.value[groupIndex].items = items;
    } else {
      menuGroups.value.push({ type, items });
    }
  }

  function setSystemGroup(type: MenuType, systems: System[]) {
    const groupIndex = systemGroups.value.findIndex((group) => group.type === type);
    if (groupIndex >= 0 && systemGroups.value[groupIndex]) {
      systemGroups.value[groupIndex].systems = systems;
    } else {
      systemGroups.value.push({ type, systems });
    }
  }

  function setSystemCards(systems: System[]) {
    systemCards.value = systems;
  }

  function setBreadcrumbItems(items: MenuLink[]) {
    breadcrumbItems.value = items;
  }

  function setActiveMenuType(type: MenuType) {
    activeMenuType.value = type;
  }

  function getAllSystems(): System[] {
    return systemGroups.value.flatMap((group) => group.systems);
  }

  function getSystemsByType(type: MenuType): System[] {
    const group = systemGroups.value.find((g) => g.type === type);
    return group?.systems ?? [];
  }

  function setCurrentQuizTitle(title: string) {
    currentQuizTitle.value = title;
  }

  function setQuizTitle(quizId: string, title: string) {
    quizTitles.value.set(quizId, title);
    saveQuizTitlesToStorage();
  }

  function getCurrentQuizTitle(quizId?: string): string {
    if (quizId) {
      return quizTitles.value.get(quizId) || '';
    }
    return currentQuizTitle.value;
  }

  function clearQuizTitle(quizId?: string) {
    if (quizId) {
      quizTitles.value.delete(quizId);
      saveQuizTitlesToStorage();
    } else {
      currentQuizTitle.value = '';
    }
  }

  // ฟังก์ชันใหม่สำหรับเรียก API และบันทึก quiz title
  function fetchAndSetQuizTitle(quizId: string): string {
    try {
      // ตรวจสอบใน store ก่อน
      const existingTitle = getCurrentQuizTitle(quizId);
      if (existingTitle) {
        return existingTitle;
      }

      // เรียก API เพื่อดึงข้อมูล quiz
      // แทนที่ด้วย API endpoint ที่ถูกต้อง
      // const response = await fetch(`/api/quiz/${quizId}`);
      // const quizData = await response.json();
      // const title = quizData.title || quizData.name || `แบบทดสอบ #${quizId}`;

      // ตอนนี้ใช้ mock data
      const title = `แบบทดสอบ #${quizId}`;

      // บันทึกลง store (และ localStorage)
      setQuizTitle(quizId, title);

      return title;
    } catch (error) {
      console.error('Error fetching quiz title:', error);
      const fallbackTitle = `แบบทดสอบ #${quizId}`;
      setQuizTitle(quizId, fallbackTitle);
      return fallbackTitle;
    }
  }

  function getActiveMenuItems(): MenuLink[] {
    const group = menuGroups.value.find((g) => g.type === activeMenuType.value);
    return group?.items ?? [];
  }

  function getBreadcrumbByType(type: MenuType): MenuLink[] {
    return breadcrumbMap.value[type] || [];
  }

  function Loading() {
    saveLoading.value = true;
    setTimeout(() => {
      saveLoading.value = false;
    }, 2000);
  }

  // Enhanced save status management functions
  function startSaveOperation(message: string = 'Saving...') {
    saveOperationsCount.value++;
    saveStatus.value = 'saving';
    saveMessage.value = message;
  }

  function completeSaveOperation(success: boolean = true, message?: string) {
    saveOperationsCount.value = Math.max(0, saveOperationsCount.value - 1);

    if (saveOperationsCount.value === 0) {
      if (success) {
        saveStatus.value = 'success';
        saveMessage.value = message || 'Saved successfully';
        // Auto-hide success message after 2 seconds
        setTimeout(() => {
          if (saveStatus.value === 'success') {
            saveStatus.value = 'idle';
            saveMessage.value = '';
          }
        }, 2000);
      } else {
        saveStatus.value = 'error';
        saveMessage.value = message || 'Saved successfully';
        // Auto-hide error message after 4 seconds
        setTimeout(() => {
          if (saveStatus.value === 'error') {
            saveStatus.value = 'idle';
            saveMessage.value = '';
          }
        }, 4000);
      }
    }
  }

  function resetSaveStatus() {
    saveStatus.value = 'idle';
    saveMessage.value = '';
    saveOperationsCount.value = 0;
  }

  return {
    menuGroups,
    menuItems,
    systemGroups,
    systemCards,
    breadcrumbItems,
    activeMenuType,
    currentQuizTitle,
    quizTitles,
    saveLoading,
    saveStatus,
    saveMessage,
    saveOperationsCount,
    setCurrentQuizTitle,
    setQuizTitle,
    getCurrentQuizTitle,
    clearQuizTitle,
    fetchAndSetQuizTitle,
    setMenuItems,
    setMenuGroup,
    setSystemGroup,
    setSystemCards,
    setBreadcrumbItems,
    setActiveMenuType,
    getAllSystems,
    getSystemsByType,
    getActiveMenuItems,
    getBreadcrumbByType,
    leftDrawerState,
    toggleLeftDrawer,
    Loading,
    startSaveOperation,
    completeSaveOperation,
    resetSaveStatus,
  };
});
