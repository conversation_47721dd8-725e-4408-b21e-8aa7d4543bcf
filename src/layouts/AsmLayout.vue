<script setup lang="ts">
import AppBreadcrumb from 'src/components/AppBreadcrumb.vue';
import MainFooter from 'src/components/MainFooter.vue';
import { useRoute } from 'vue-router';
import { computed, watch } from 'vue';
import { useGlobalStore } from 'src/stores/global';
import MainHeader from 'src/components/MainHeader.vue';
import LeftDrawer from 'src/components/common/LeftDrawer.vue';
import { allDrawerMenu } from 'src/data/menu';
import type { MenuType } from 'src/types/app';

const route = useRoute();
const globalStore = useGlobalStore();

// Dynamically determine menu type based on current route path
const menuType = computed(() => {
  const queryType = route.query.type as MenuType;
  if (queryType) return queryType;

  const path = route.path;
  if (path.startsWith('/evaluate')) return 'form';
  if (path.startsWith('/quiz')) return 'quiz';
  if (path.startsWith('/ums')) return 'ums';

  return 'default';
});

const fullBreadcrumbItems = computed(() => globalStore.getBreadcrumbByType(menuType.value));

const breadcrumbItems = computed(() => {
  const currentPath = route.path;
  const result = [];

  if (fullBreadcrumbItems.value.length > 0) {
    const home = fullBreadcrumbItems.value[0];
    if (home) result.push(home);
  }

  for (let i = 1; i < fullBreadcrumbItems.value.length; i++) {
    const item = fullBreadcrumbItems.value[i];
    if (currentPath === item?.link) {
      result.push(item);
      break;
    }
    if (item?.link && currentPath.startsWith(item.link)) {
      result.push(item);
    }
  }

  return result;
});

// Set active menu type in global store for consistency
watch(
  menuType,
  (type) => {
    globalStore.setActiveMenuType(type);
  },
  { immediate: true },
);
</script>

<template>
  <q-layout view="hHh lpR fff">
    <MainHeader />
    <q-page-container>
      <LeftDrawer :menu="allDrawerMenu" />
      <AppBreadcrumb :items="breadcrumbItems" />
      <router-view />
    </q-page-container>
    <MainFooter />
  </q-layout>
</template>

<style scoped></style>
