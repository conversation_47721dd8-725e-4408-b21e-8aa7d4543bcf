<template>
  <q-page padding>
    <q-table title="จัดการบทบาท" :rows="mockUsers" :columns="userColumns" row-key="id">
      <template #top-right>
        <div>
          <q-btn color="accent" label="เพิ่มบทบาท" icon="add" @click="onClickNewUser" />
        </div>
      </template>
      <template #body-cell-actions>
        <q-td class="row justify-center">
          <q-btn icon="edit" padding="xs" @click="onClickEdit({} as User)"></q-btn>
          <q-btn icon="delete" padding="xs"></q-btn>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { userColumns } from 'src/data/table_columns';
import type { User } from 'src/types/models';
import { defineAsyncComponent, ref } from 'vue';

const mockUsers = ref<User[]>([
  {
    id: 1,
    roleId: 1, // Added roleId
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123', // Added password
    // roles: [{ id: 1, userId: 1, name: 'Admin' }], // Optional: roles array
  },
  {
    id: 2,
    roleId: 2, // Added roleId
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'password456', // Added password
    // roles: [{ id: 2, userId: 2, name: 'Editor' }], // Optional: roles array
  },
  {
    id: 3,
    roleId: 3, // Added roleId
    name: 'Alice Johnson',
    email: '<EMAIL>',
    password: 'password789', // Added password
    // roles: [{ id: 3, userId: 3, name: 'Viewer' }], // Optional: roles array
  },
  {
    id: 4,
    roleId: 2, // Added roleId
    name: 'Bob Brown',
    email: '<EMAIL>',
    password: 'password101', // Added password
    // roles: [{ id: 4, userId: 4, name: 'Editor' }], // Optional: roles array
  },
  {
    id: 5,
    roleId: 1, // Added roleId
    name: 'Charlie Davis',
    email: '<EMAIL>',
    password: 'password112', // Added password
    // roles: [{ id: 5, userId: 5, name: 'Admin' }], // Optional: roles array
  },
]);

const $q = useQuasar();

const onClickNewUser = () => {
  // Logic to open the new user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/userForm.vue')),
    persistent: true,
    componentProps: {
      user: null, // Pass null for new user
    },
  });
};

const onClickEdit = (user: User) => {
  // Logic to open the edit user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/userForm.vue')),
    persistent: true,
    componentProps: {
      user, // Pass the selected user for editing
    },
  });
};
</script>

<style scoped></style>
