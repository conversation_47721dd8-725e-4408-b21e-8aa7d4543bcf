import type { QTableProps } from 'quasar';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { User } from 'src/types/models';
import { formatParams } from 'src/utils/utils';

export class UserService {
  private static path = 'users'; // Assuming 'api' is imported from a common API utility

  // Method to get user information by ID
  static createUser(dto: User) {
    return api.post<User>(`${this.path}`, dto);
  }

  static getUserById(id: number) {
    return api.get<User>(`${this.path}/${id}`);
  }

  // Method to update user information
  static updateUser(id: number, data: Partial<User>) {
    return api.patch<User>(`${this.path}/${id}`, data);
  }

  // Method to delete a user
  static deleteUser(id: number) {
    return api.delete<User>(`${this.path}/${id}`);
  }

  // Method to get all users with pagination
  static getUsers(pagination: QTableProps['pagination']) {
    const params = formatParams(pagination);
    return api.get<DataResponse<User>>(`${this.path}`, { params });
  }
}
